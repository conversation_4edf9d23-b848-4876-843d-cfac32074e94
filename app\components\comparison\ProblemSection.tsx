import { Button } from "@/components/ui/button";
import { CheckCircle, X } from "lucide-react";

interface ProblemSectionProps {
  painPoints: string;
  commonChallenges: string[];
  painPointsClosing: string;
  solutionIntroduction: string;
  solutionDescription: string;
  keyFeatures: Array<{
    name: string;
    description: string;
  }>;
}

export default function ProblemSection({
  painPoints,
  commonChallenges,
  painPointsClosing,
  solutionIntroduction,
  solutionDescription,
  keyFeatures,
}: ProblemSectionProps) {
  return (
    <section className="py-24 px-6 bg-muted/50">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-8 sm:mb-10 text-foreground">
          {painPoints}
        </h2>

        <div className="grid md:grid-cols-3 gap-6 mb-8 max-w-4xl mx-auto">
          {commonChallenges.map((challenge, index) => (
            <div
              key={index}
              className="p-5 bg-destructive/10 border border-destructive/20 rounded-lg"
            >
              <X className="h-8 w-8 text-destructive mx-auto mb-4" />
              <p className="text-destructive font-medium">{challenge}</p>
            </div>
          ))}
        </div>

        <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 text-muted-foreground">
          {painPointsClosing}
        </p>

        <div className="bg-card p-6 sm:p-8 rounded-xl shadow-lg border border-border max-w-5xl mx-auto">
          <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3 mt-5 sm:mb-4 text-primary">
            {solutionIntroduction}
          </h3>
          <p className="text-base sm:text-lg md:text-xl mb-4 sm:mb-6 text-muted-foreground">
            {solutionDescription}
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 py-7">
            {keyFeatures.map((feature, index) => (
              <div
                key={index}
                className="p-6 bg-primary/5 border border-primary/10 rounded-lg w-full max-w-xs mx-auto"
              >
                <CheckCircle className="h-6 w-6 text-primary mb-2" />
                <h4 className="font-semibold mb-2 text-foreground">
                  {feature.name}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-8">
          <a
            href="https://william.tryinloop.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block"
          >
            <Button
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 px-8 py-3 cursor-pointer shadow-md hover:shadow-lg"
            >
              Try Inloop for Free
            </Button>
          </a>
        </div>
      </div>
    </section>
  );
}
