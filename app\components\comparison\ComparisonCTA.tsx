"use client";

import { motion } from "framer-motion";
import AnimatedButton from "../common/AnimatedButton";
import { CheckCircle } from "lucide-react";

interface ComparisonCTAProps {
  title?: string;
  description?: string;
  buttonText: string;
  buttonHref?: string;
  variant?: "default" | "accent" | "minimal";
  size?: "sm" | "md" | "lg";
  className?: string;
  showTrustIndicators?: boolean;
}

export default function ComparisonCTA({
  title,
  description,
  buttonText,
  buttonHref = "https://william.tryinloop.com",
  variant = "default",
  size = "md",
  className = "",
  showTrustIndicators = false,
}: ComparisonCTAProps) {
  const sizeClasses = {
    sm: "p-6",
    md: "p-8",
    lg: "p-12",
  };

  const titleSizeClasses = {
    sm: "text-lg md:text-xl font-semibold mb-3",
    md: "text-xl md:text-2xl font-semibold mb-4",
    lg: "text-2xl md:text-3xl lg:text-4xl font-bold mb-6",
  };

  const descriptionSizeClasses = {
    sm: "text-sm text-muted-foreground mb-4",
    md: "text-base md:text-lg text-muted-foreground mb-6",
    lg: "text-lg md:text-xl text-muted-foreground mb-8",
  };

  const getVariantClasses = () => {
    switch (variant) {
      case "accent":
        return "bg-gradient-to-br from-primary/10 via-secondary/5 to-background/5 border-primary/20";
      case "minimal":
        return "bg-card border-border";
      default:
        return "bg-gradient-to-br from-background/60 to-background/40 border-primary/20";
    }
  };

  return (
    <motion.div
      className={`
        relative overflow-hidden rounded-2xl border shadow-lg shadow-primary/10
        backdrop-blur-sm transition-all duration-500 hover:shadow-xl hover:shadow-primary/15
        ${getVariantClasses()}
        ${sizeClasses[size]}
        ${className}
      `}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
      whileHover={{ scale: 1.02 }}
    >
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 pointer-events-none" />

      {/* Content */}
      <div className="relative z-10 text-center">
        <h3
          className={`${titleSizeClasses[size]} tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground/95 to-foreground/80`}
        >
          {title}
        </h3>

        {description && (
          <p className={descriptionSizeClasses[size]}>{description}</p>
        )}

        <div className="flex flex-col items-center gap-4">
          <AnimatedButton
            size={size === "lg" ? "lg" : "md"}
            showArrow={true}
            href={buttonHref}
            target="_blank"
            className="inline-flex"
          >
            {buttonText}
          </AnimatedButton>

          {showTrustIndicators && (
            <div className="flex items-center justify-center gap-x-6 text-sm text-muted-foreground flex-wrap">
              <span className="flex items-center gap-x-1 whitespace-nowrap">
                <CheckCircle className="w-4 h-4 text-primary" />
                No Credit Card Required
              </span>
              <span className="flex items-center gap-x-1 whitespace-nowrap">
                <CheckCircle className="w-4 h-4 text-primary" />
                Free Forever Plan
              </span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
