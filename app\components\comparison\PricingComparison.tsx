import ComparisonCTA from "./ComparisonCTA";

interface PricingComparisonProps {
  headline: string;
  description: string;
  pricingTable: Array<{
    plan: string;
    inloop: string;
    competitor: string;
  }>;
  valueProposition: string;
  competitorName: string;
  ctaContent: {
    title?: string;
    description: string;
    buttonText: string;
  };
}

export default function PricingComparison({
  headline,
  description,
  pricingTable,
  valueProposition,
  competitorName,
  ctaContent,
}: PricingComparisonProps) {
  return (
    <section className="py-24 px-6 bg-muted/50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 text-foreground">
            {headline}
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
            {description}
          </p>
        </div>

        <div className="overflow-x-auto mb-12">
          <table className="w-full bg-card rounded-xl shadow-lg border border-border">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left p-3 sm:p-4 md:p-6 font-semibold text-foreground text-sm sm:text-base">
                  Plan
                </th>
                <th className="text-left p-3 sm:p-4 md:p-6 font-semibold text-primary text-sm sm:text-base">
                  Inloop
                </th>
                <th className="text-left p-3 sm:p-4 md:p-6 font-semibold text-muted-foreground text-sm sm:text-base">
                  {competitorName}
                </th>
              </tr>
            </thead>
            <tbody>
              {pricingTable.map((row, index) => (
                <tr
                  key={index}
                  className="border-b border-border hover:bg-muted/50"
                >
                  <td className="p-3 sm:p-4 md:p-6 font-medium text-foreground text-sm sm:text-base">
                    {row.plan}
                  </td>
                  <td className="p-3 sm:p-4 md:p-6 text-primary font-medium text-sm sm:text-base">
                    {row.inloop}
                  </td>
                  <td className="p-3 sm:p-4 md:p-6 text-muted-foreground text-sm sm:text-base">
                    {row.competitor}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <ComparisonCTA
          title={valueProposition}
          description={ctaContent.description}
          buttonText="Try Inloop for Free"
          variant="accent"
          size="sm"
        />
      </div>
    </section>
  );
}
