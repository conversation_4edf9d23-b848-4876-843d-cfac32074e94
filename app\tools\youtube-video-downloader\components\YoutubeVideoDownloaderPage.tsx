"use client";

import { useState } from "react";
import TextGradient from "@/components/ui/text-gradient";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Ripple } from "@/components/magicui/ripple";
import Meta from "@/app/components/meta";
import FAQSection from "@/app/components/FAQSection";
import {
  detectPlatform,
  VideoOption,
  AudioOption,
} from "@/lib/platform-config";
import React from "react";
import Link from "next/link";
import { downloadVideoAction } from "@/app/actions/video-download";
import { useGtmTrack } from "@/lib/tracking";

const RippleBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute inset-0 [mask-image:radial-gradient(circle_at_center,white,transparent_80%)]">
        <Ripple
          mainCircleSize={400}
          mainCircleOpacity={0.25}
          numCircles={8}
          className="opacity-70 [--ripple-bg:hsl(var(--primary)_/_0.15)]"
          style={{ "--foreground": "var(--primary)" } as React.CSSProperties}
        />
      </div>
    </div>
  );
};

const faqItems = [
  {
    question: "Is this tool free?",
    answer:
      "Yes, this YouTube video downloader tool is completely free to use—no hidden fees or sign-ups required.",
  },
  {
    question: "Does Inloop offer more free tools?",
    answer: (
      <>
        Absolutely! Inloop provides additional free YouTube Tools designed to
        help you increase your engagement and grow your presence on the
        platform. Check our AI Agent William.{" "}
        <Link
          href="https://tryinloop.com/"
          target="_blank"
          rel="noopener noreferrer"
          className="text-primary hover:underline"
        >
          Learn more
        </Link>
      </>
    ),
  },
  {
    question: "Is it legal to download videos from YouTube?",
    answer:
      "Yes, downloading YouTube videos for personal use is legal, but not for commercial distribution. Always respect copyright laws and review YouTube's terms of service, especially when accessing private or restricted content.",
  },
  {
    question: "Can I download YouTube videos in Full HD or 4K?",
    answer:
      "Absolutely. If the original video supports HD or 4K, Inloop's YouTube Downloader lets you save videos in stunning quality. Enjoy crystal-clear offline playback across all your devices.",
  },
  {
    question: "How to download YouTube videos on a mobile device?",
    answer: (
      <>
        <p className="mb-3">
          You can easily save YouTube videos to your phone using Inloop's
          mobile-optimized downloader:
        </p>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>
            <strong>Copy the Video URL:</strong> Open YouTube and copy the video
            link.
          </li>
          <li>
            <strong>Visit Inloop's Free YouTube Downloader:</strong> Navigate to
            Inloop's YouTube video saver in your mobile browser.
          </li>
          <li>
            <strong>Paste the URL:</strong> Enter the copied link in the tool.
          </li>
          <li>
            <strong>Download:</strong> Tap the download button to save the video
            directly to your device.
          </li>
        </ol>
        <p className="mt-3">
          Inloop offers one of the best YouTube video downloaders for Android
          and iOS—no registration needed, lightning-fast conversion, and perfect
          for both YouTube videos and Shorts.
        </p>
      </>
    ),
  },
  {
    question: "How to download YouTube videos on a computer?",
    answer: (
      <>
        <p className="mb-3">
          To download videos from YouTube to your desktop or laptop, follow
          these simple steps with Inloop:
        </p>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>
            <strong>Copy the YouTube Video Link:</strong> Open YouTube on your
            PC and copy the desired video URL.
          </li>
          <li>
            <strong>Open Inloop's Video Downloader:</strong> Head to the Inloop
            YouTube downloader page.
          </li>
          <li>
            <strong>Paste the Link:</strong> Insert the video URL into the input
            field.
          </li>
          <li>
            <strong>Choose Quality & Download:</strong> Select from multiple
            resolution options and hit download.
          </li>
        </ol>
        <p className="mt-3">
          Inloop supports high-speed downloads, video format conversion, and is
          an excellent choice for YouTube Shorts, full-length videos, and even
          Chrome browser users.
        </p>
      </>
    ),
  },
  {
    question: "Can I download YouTube videos on an iPhone or iPad?",
    answer:
      "Yes. Use Inloop's browser-based YouTube downloader to save videos on iOS devices. Just paste the video link and download for smooth offline viewing. The tool is fully compatible with iPhones and iPads.",
  },
  {
    question: "Can I download entire YouTube playlists?",
    answer:
      "Yes, you can download YouTube playlists by adding each video URL one by one into Inloop's downloader. Save all your favorite videos for offline access anytime, anywhere.",
  },
  {
    question:
      "Are there limits to the number of YouTube videos I can download?",
    answer:
      "No. With Inloop, you can download unlimited videos from YouTube for personal use. Just ensure you follow ethical usage and avoid excessive or automated downloading to stay within fair use policies.",
  },
];

export default function YoutubeVideoDownloaderPage() {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [videoOptions, setVideoOptions] = useState<VideoOption[]>([]);
  const [audioOptions, setAudioOptions] = useState<AudioOption[]>([]);
  const [selectedVideoUrl, setSelectedVideoUrl] = useState("");
  const [selectedAudioUrl, setSelectedAudioUrl] = useState("");
  const [title, setTitle] = useState<string>("");
  const [thumbnail, setThumbnail] = useState<string>("");
  const [activeTab, setActiveTab] = useState<"video" | "audio">("video");

  const { downloadVideoTrack } = useGtmTrack();
  const currentPlatform = detectPlatform(url);
  const isYoutubeUrl = currentPlatform === "Youtube";

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setUrl(newUrl);
    setError("");
    setVideoOptions([]);
    setAudioOptions([]);
    setSelectedVideoUrl("");
    setSelectedAudioUrl("");
    setTitle("");
    setThumbnail("");
  };

  const handleVideoQualitySelect = (videoUrl: string) => {
    setSelectedVideoUrl(videoUrl);
  };

  const handleAudioQualitySelect = (audioUrl: string) => {
    setSelectedAudioUrl(audioUrl);
  };

  const handleGetVideo = async () => {
    if (!url) {
      setError("Please enter a YouTube video URL");
      return;
    }

    if (!isYoutubeUrl) {
      setError("Please enter a valid YouTube video URL");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const result = await downloadVideoAction(url);

      if (!result.success) {
        setError(result.error || "An error occurred while getting the video");
        return;
      }

      if (result.data) {
        setVideoOptions(result.data.videoOptions);
        setAudioOptions(result.data.audioOptions || []);
        setTitle(result.data.title || "YouTube Video");

        if (result.data.thumbnail) {
          setThumbnail(result.data.thumbnail);
        }

        if (result.data.videoOptions.length === 1) {
          setSelectedVideoUrl(result.data.videoOptions[0].url);
        }
        if (result.data.audioOptions && result.data.audioOptions.length === 1) {
          setSelectedAudioUrl(result.data.audioOptions[0].url);
        }

        downloadVideoTrack("youtube");
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while getting the video"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadThumbnail = async () => {
    if (!thumbnail) return;

    window.open(thumbnail, "_blank");
  };

  const openDownloadWindow = (type: "video" | "audio" = "video") => {
    const selectedUrl = type === "video" ? selectedVideoUrl : selectedAudioUrl;

    if (!selectedUrl) {
      setError(`Please select a ${type} quality to download`);
      return;
    }

    window.open(selectedUrl, "_blank");
  };

  const handleReset = () => {
    setUrl("");
    setVideoOptions([]);
    setAudioOptions([]);
    setSelectedVideoUrl("");
    setSelectedAudioUrl("");
    setTitle("");
    setThumbnail("");
    setError("");
    setActiveTab("video");
  };

  const hasVideoOptions = videoOptions.length > 0;
  const hasAudioOptions = audioOptions.length > 0;
  const hasAnyOptions = hasVideoOptions || hasAudioOptions;
  const showVideoQualitySelector = videoOptions.length > 1;
  const showAudioQualitySelector = audioOptions.length > 1;

  return (
    <main className="relative overflow-hidden min-h-screen bg-background">
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none"></div>
      <div className="sm:block hidden absolute top-20 left-[30%] opacity-[0.08] transform scale-150 gradient-blob"></div>

      <div className="relative pt-32 pb-20">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-16">
            <h1 className="mb-6 text-4xl sm:text-6xl leading-tight font-medium text-foreground">
              <TextGradient txt="YouTube" /> Video Downloader
            </h1>
            <p className="text-lg sm:text-xl font-normal text-muted-foreground max-w-xl mx-auto leading-relaxed">
              Download YouTube videos and audio in multiple qualities and save
              them to your device for offline viewing or content creation.
            </p>
          </div>

          <div className="relative">
            <div className="relative p-8 sm:p-12 rounded-3xl shadow shadow-primary/20 overflow-hidden bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-md border border-primary/20">
              <RippleBackground />
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>

              <div className="relative z-10">
                {!hasAnyOptions ? (
                  <div className="space-y-8">
                    <h2 className="text-2xl font-semibold text-foreground text-center">
                      Download YouTube Video
                    </h2>
                    <div className="max-w-2xl mx-auto space-y-6">
                      <div>
                        <label className="block text-sm font-medium mb-2 text-muted-foreground text-left">
                          YouTube Video URL
                        </label>
                        <p className="text-sm text-muted-foreground/80 mb-3 text-left">
                          Paste the URL of the YouTube video you want to
                          download
                        </p>
                      </div>

                      <div className="flex gap-3 items-center">
                        <input
                          type="text"
                          className="flex-1 px-4 py-3 rounded-lg border border-border bg-background/80 backdrop-blur-sm text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all h-12"
                          placeholder="https://www.youtube.com/watch?v=..."
                          value={url}
                          onChange={handleInputChange}
                        />
                        <Button
                          onClick={handleGetVideo}
                          disabled={loading || !url}
                          className={cn(
                            "px-6 py-3 whitespace-nowrap cursor-pointer h-12",
                            "bg-primary text-primary-foreground hover:bg-primary/90"
                          )}
                        >
                          {loading ? (
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"></div>
                              Fetching...
                            </div>
                          ) : (
                            "Get Video"
                          )}
                        </Button>
                      </div>

                      {url && (
                        <div className="flex items-center gap-2 text-sm">
                          {isYoutubeUrl ? (
                            <div className="flex items-center gap-2 text-primary">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              YouTube URL detected
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                              Please enter a valid YouTube URL
                            </div>
                          )}
                        </div>
                      )}

                      {error && (
                        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 text-destructive">⚠️</div>
                            <span className="text-destructive text-sm font-medium">
                              {error}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {/* Video Info Section with Thumbnail */}
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-primary/20 flex items-center justify-center">
                        <svg
                          className="w-6 h-6 text-primary"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      <h3 className="text-xl font-semibold text-foreground mb-4">
                        Ready for Download!
                      </h3>

                      {thumbnail && (
                        <div className="max-w-md mx-auto mb-4">
                          <div className="relative group">
                            <img
                              src={thumbnail}
                              alt={title || "Video thumbnail"}
                              className="w-full rounded-lg shadow-lg border border-border"
                              loading="lazy"
                            />
                            <button
                              onClick={handleDownloadThumbnail}
                              className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm border border-border rounded-lg p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background/90 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
                              title="Download thumbnail"
                            >
                              <svg
                                className="w-4 h-4 text-foreground"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}

                      {title && (
                        <p className="text-foreground text-sm font-medium bg-muted/30 rounded px-4 py-2 mt-2 inline-block max-w-md">
                          {title}
                        </p>
                      )}
                    </div>

                    {/* Tab Selection */}
                    {hasVideoOptions && hasAudioOptions && (
                      <div className="flex justify-center">
                        <div className="bg-muted/30 p-1 rounded-lg">
                          <button
                            onClick={() => setActiveTab("video")}
                            className={cn(
                              "px-4 py-2 rounded-md text-sm font-medium transition-all cursor-pointer",
                              activeTab === "video"
                                ? "bg-primary text-primary-foreground"
                                : "text-muted-foreground hover:text-foreground"
                            )}
                          >
                            Video ({videoOptions.length})
                          </button>
                          <button
                            onClick={() => setActiveTab("audio")}
                            className={cn(
                              "px-4 py-2 rounded-md text-sm font-medium transition-all cursor-pointer",
                              activeTab === "audio"
                                ? "bg-primary text-primary-foreground"
                                : "text-muted-foreground hover:text-foreground"
                            )}
                          >
                            Audio ({audioOptions.length})
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Video Quality Selector */}
                    {activeTab === "video" &&
                      hasVideoOptions &&
                      showVideoQualitySelector && (
                        <div className="max-w-md mx-auto space-y-4">
                          <h4 className="text-sm font-medium text-foreground text-center">
                            Select Video Quality:
                          </h4>
                          <div className="relative">
                            <select
                              value={selectedVideoUrl}
                              onChange={(e) =>
                                handleVideoQualitySelect(e.target.value)
                              }
                              className="w-full appearance-none bg-background/80 backdrop-blur-sm border border-border rounded-lg px-4 py-3 pr-10 text-foreground font-medium focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 cursor-pointer"
                            >
                              <option value="" disabled>
                                Choose video quality...
                              </option>
                              {videoOptions.map((option, index) => (
                                <option
                                  key={index}
                                  value={option.url}
                                  className="bg-background text-foreground py-2"
                                >
                                  {option.label}{" "}
                                  {option.sizeText && `(${option.sizeText})`}
                                </option>
                              ))}
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                              <svg
                                className="w-5 h-5 text-muted-foreground"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </div>
                          </div>
                          {selectedVideoUrl && (
                            <div className="flex items-center gap-2 text-sm text-primary text-center justify-center">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              <span>
                                Selected:{" "}
                                {
                                  videoOptions.find(
                                    (opt) => opt.url === selectedVideoUrl
                                  )?.label
                                }
                              </span>
                            </div>
                          )}
                        </div>
                      )}

                    {/* Audio Quality Selector */}
                    {activeTab === "audio" &&
                      hasAudioOptions &&
                      showAudioQualitySelector && (
                        <div className="max-w-md mx-auto space-y-4">
                          <h4 className="text-sm font-medium text-foreground text-center">
                            Select Audio Quality:
                          </h4>
                          <div className="relative">
                            <select
                              value={selectedAudioUrl}
                              onChange={(e) =>
                                handleAudioQualitySelect(e.target.value)
                              }
                              className="w-full appearance-none bg-background/80 backdrop-blur-sm border border-border rounded-lg px-4 py-3 pr-10 text-foreground font-medium focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 cursor-pointer"
                            >
                              <option value="" disabled>
                                Choose audio format...
                              </option>
                              {audioOptions.map((option, index) => (
                                <option
                                  key={index}
                                  value={option.url}
                                  className="bg-background text-foreground py-2"
                                >
                                  {option.label}
                                </option>
                              ))}
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                              <svg
                                className="w-5 h-5 text-muted-foreground"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </div>
                          </div>
                          {selectedAudioUrl && (
                            <div className="flex items-center gap-2 text-sm text-primary text-center justify-center">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              <span>
                                Selected:{" "}
                                {
                                  audioOptions.find(
                                    (opt) => opt.url === selectedAudioUrl
                                  )?.label
                                }
                              </span>
                            </div>
                          )}
                        </div>
                      )}

                    <div className="max-w-md mx-auto space-y-6">
                      {loading && (
                        <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 border-3 border-primary border-t-transparent rounded-full animate-spin"></div>
                            <div>
                              <p className="text-primary font-medium">
                                Downloading your {activeTab}...
                              </p>
                              <p className="text-primary/80 text-sm">
                                Please wait while we prepare your download
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex flex-col gap-3">
                        <Button
                          onClick={() => openDownloadWindow(activeTab)}
                          disabled={
                            loading ||
                            (activeTab === "video" &&
                              showVideoQualitySelector &&
                              !selectedVideoUrl) ||
                            (activeTab === "audio" &&
                              showAudioQualitySelector &&
                              !selectedAudioUrl)
                          }
                          className={cn(
                            "w-full bg-primary hover:bg-primary/90 text-primary-foreground cursor-pointer",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                        >
                          {loading ? (
                            <div className="flex items-center justify-center gap-2">
                              <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"></div>
                              Downloading...
                            </div>
                          ) : (
                            <div className="flex items-center justify-center gap-2">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                              Download{" "}
                              {activeTab === "video" ? "Video" : "Audio"}
                            </div>
                          )}
                        </Button>

                        <Button
                          onClick={handleReset}
                          disabled={loading}
                          variant="outline"
                          className="w-full backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                        >
                          Download Another
                        </Button>
                      </div>

                      <p className="text-xs text-muted-foreground text-center">
                        {loading
                          ? `Your ${activeTab} download is being prepared and will start automatically`
                          : (activeTab === "video" &&
                              showVideoQualitySelector &&
                              !selectedVideoUrl) ||
                            (activeTab === "audio" &&
                              showAudioQualitySelector &&
                              !selectedAudioUrl)
                          ? `Please select a ${activeTab} quality to continue`
                          : `Click 'Download ${
                              activeTab === "video" ? "Video" : "Audio"
                            }' to open. Right-click to save on desktop, or press and hold on mobile.`}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto mt-8">
            <div className="bg-muted/30 border border-border rounded-lg p-6 backdrop-blur-sm">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                    <span className="text-muted-foreground text-sm">⚠️</span>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-foreground mb-2">
                    Important Legal Notice
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Only download videos for personal use or when you have
                    explicit permission from the content creator. Always respect
                    copyright laws, platform terms of service, and intellectual
                    property rights when using downloaded content.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: How to Download */}
      <section className="py-16 w-full max-w-6xl mx-auto px-4">
        <Meta
          heading="How to download a YouTube video?"
          subheading="Follow these simple steps to download any YouTube video"
          iconText="Step by Step Guide"
          className="mb-12"
        />

        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/20 flex items-center justify-center">
              <span className="text-2xl font-bold text-primary">1</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">
              Copy the video URL
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Navigate to YouTube and copy the URL of the video you want to
              download from the address bar or share button.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/20 flex items-center justify-center">
              <span className="text-2xl font-bold text-primary">2</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">
              Paste the URL in text box
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Paste the copied YouTube URL into the text box on our YouTube
              Video Downloader tool above.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/20 flex items-center justify-center">
              <span className="text-2xl font-bold text-primary">3</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">
              Click on Download Video button
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Click the &quot;Get Video&quot; button to process the URL, then
              select your preferred quality and download.
            </p>
          </div>
        </div>
      </section>

      {/* Section 3: William CTA */}
      <section className="py-24 overflow-hidden relative">
        <div className="max-w-6xl mx-auto px-4 relative">
          <div className="relative p-12 sm:p-20 rounded-3xl shadow shadow-primary/20 overflow-hidden bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-md border border-primary/20">
            <RippleBackground />
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>

            <div className="relative z-10 text-center">
              <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-8">
                <span>🤖</span>
                AI Agent for YouTube
              </div>

              <h2 className="mb-6 text-3xl sm:text-5xl leading-tight font-medium text-foreground">
                Inloop AI Agent <TextGradient txt="William" /> for YouTube
              </h2>

              <p className="mx-auto mb-12 text-lg md:text-xl lg:text-xl text-muted-foreground/90 leading-relaxed max-w-3xl">
                William is an AI Agent for YouTube. He automates YouTube Content
                creation using Trending Topics, Industry News, and Viral
                Templates. Use him to build a Personal Brand or turn your team
                into YouTube Micro Influencers.
              </p>

              <div className="flex justify-center">
                <Button asChild className="rounded-full cursor-pointer">
                  <Link
                    href="https://william.tryinloop.com/login"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Try William for Free
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 4: YouTube Video Tool Benefits */}
      <section className="py-16 w-full max-w-6xl mx-auto px-4">
        <Meta
          heading="YouTube Video Downloader Tool - Top Advantages"
          subheading="Unlock powerful features with Inloop, your go-to YouTube video downloader tool"
          iconText="Free Video Tool"
          className="mb-12"
        />

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              title: "Watch Anytime, Anywhere",
              description:
                "Save YouTube videos for offline viewing—even when you're without internet access.",
              icon: "📱",
            },
            {
              title: "Fast & Easy Downloads",
              description:
                "Simply paste the YouTube link into Inloop and download your video instantly.",
              icon: "⚡",
            },
            {
              title: "Choose Your Quality",
              description:
                "Inloop lets you select your preferred resolution—HD, Full HD, or standard—to suit your needs.",
              icon: "🎯",
            },
            {
              title: "Device-Friendly",
              description:
                "Download videos effortlessly on mobile, tablet, or desktop. Seamlessly transfer between devices.",
              icon: "💻",
            },
            {
              title: "Multiple Format Support",
              description:
                "Convert and download in MP4 or other compatible formats for playback on any platform.",
              icon: "🔄",
            },
            {
              title: "Support for Shorts & More",
              description:
                "Whether it's full-length content or YouTube Shorts, Inloop handles it all with speed and precision.",
              icon: "🎬",
            },
          ].map((feature, index) => (
            <div
              key={index}
              className="bg-card/60 border border-primary/20 rounded-lg p-6 backdrop-blur-sm"
            >
              <div className="text-2xl mb-4">{feature.icon}</div>
              <h4 className="font-semibold text-foreground mb-3">
                {feature.title}
              </h4>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-muted-foreground">
            Enjoy fast, flexible, and reliable video downloads with Inloop—your
            trusted YouTube downloader online.
          </p>
        </div>
      </section>

      {/* Section 5: FAQ */}
      <FAQSection
        heading="YouTube Video Downloader Tool FAQ"
        subheading="Common questions about our free YouTube video downloader"
        iconText="Frequently Asked"
        faqItems={faqItems}
        className="px-4"
      />
    </main>
  );
}
