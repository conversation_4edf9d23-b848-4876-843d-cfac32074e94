import { Button } from "@/components/ui/button";

interface FeatureComparisonProps {
  headline: string;
  features: Array<{
    name: string;
    inloop: string;
    competitor: string;
  }>;
  conclusion: string;
  competitorName: string;
}

export default function FeatureComparison({
  headline,
  features,
  conclusion,
  competitorName,
}: FeatureComparisonProps) {
  return (
    <section className="py-24 px-6">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 text-foreground">
          {headline}
        </h2>

        <div className="grid gap-6 sm:gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-card rounded-xl shadow-lg p-4 sm:p-6 md:p-8 border border-border mx-4 sm:mx-0"
            >
              <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-4 sm:mb-6 text-foreground">
                {feature.name}
              </h3>
              <div className="grid md:grid-cols-2 gap-4 sm:gap-6">
                <div className="border-l-4 border-primary pl-4 sm:pl-6">
                  <h4 className="font-semibold text-primary mb-2 sm:mb-3 text-sm sm:text-base">
                    Inloop
                  </h4>
                  <p className="text-foreground text-sm sm:text-base">
                    {feature.inloop}
                  </p>
                </div>
                <div className="border-l-4 border-border pl-4 sm:pl-6">
                  <h4 className="font-semibold text-muted-foreground mb-2 sm:mb-3 text-sm sm:text-base">
                    {competitorName}
                  </h4>
                  <p className="text-muted-foreground text-sm sm:text-base">
                    {feature.competitor}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8 sm:mt-12">
          <p className="text-lg sm:text-xl md:text-2xl font-semibold mb-4 sm:mb-6 text-foreground">
            {conclusion}
          </p>
          <a
            href="https://william.tryinloop.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block"
          >
            <Button
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 px-8 py-3 cursor-pointer shadow-md hover:shadow-lg"
            >
              Try Inloop for Free
            </Button>
          </a>
        </div>
      </div>
    </section>
  );
}
