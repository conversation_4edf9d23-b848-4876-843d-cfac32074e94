"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { ShineBorder } from "@/components/magicui/shine-border";
import { CircleLogo } from "@/app/components/circle-logo";

interface ComparisonItem {
  name: string;
  slug: string;
  description: string;
  gradient: string;
}

const comparisonItems: ComparisonItem[] = [
  {
    name: "Tap<PERSON>",
    slug: "taplio-vs-inloop",
    description: "Personal branding and LinkedIn growth",
    gradient: "from-blue-500 to-cyan-500",
  },
  {
    name: "MagicPost",
    slug: "magicpost-vs-inloop",
    description: "LinkedIn content scheduling",
    gradient: "from-purple-500 to-pink-500",
  },
  {
    name: "SuperPen",
    slug: "superpen-vs-inloop",
    description: "AI writing assistant",
    gradient: "from-green-500 to-emerald-500",
  },
  {
    name: "Depost",
    slug: "depost-vs-inloop",
    description: "Social media management",
    gradient: "from-orange-500 to-red-500",
  },
  {
    name: "AuthoredUp",
    slug: "authoredup-vs-inloop",
    description: "LinkedIn post editor",
    gradient: "from-indigo-500 to-purple-500",
  },
  {
    name: "EasyGen",
    slug: "easygen-vs-inloop",
    description: "AI content generation",
    gradient: "from-teal-500 to-blue-500",
  },
  {
    name: "Taplio",
    slug: "taplio-vs-inloop",
    description: "Personal branding and LinkedIn growth",
    gradient: "from-blue-500 to-cyan-500",
  },
];

interface OtherComparisonsProps {
  currentCompetitor?: string;
}

export default function OtherComparisons({
  currentCompetitor,
}: OtherComparisonsProps) {
  // Filter out the current competitor from the list
  const filteredComparisons = comparisonItems.filter(
    (item) => item.name.toLowerCase() !== currentCompetitor?.toLowerCase()
  );

  return (
    <section className="py-16 px-4 bg-gradient-to-b from-background to-background/80">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Other Comparisons
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            See how Inloop outperforms other leading alternatives in the market
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredComparisons.map((comparison, index) => (
            <motion.div
              key={comparison.slug}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <ComparisonCard comparison={comparison} />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

function ComparisonCard({ comparison }: { comparison: ComparisonItem }) {
  return (
    <Link href={`/comparison/${comparison.slug}`}>
      <div className="group relative overflow-hidden rounded-2xl bg-card/50 backdrop-blur-sm border border-border/50 p-6 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] cursor-pointer">
        {/* Shine border effect */}
        <ShineBorder
          className="absolute inset-0"
          borderWidth={1}
          duration={8}
          shineColor={["#ff6b6b", "#4ecdc4", "#45b7d1"]}
        />

        {/* Background gradient overlay */}
        <div
          className={cn(
            "absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-2xl",
            `bg-gradient-to-br ${comparison.gradient}`
          )}
        />

        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              {/* Inloop logo */}
              <CircleLogo size="xs" />
              <span className="text-sm font-medium text-muted-foreground">
                vs
              </span>
              {/* Competitor */}
              <div className="w-8 h-8 rounded-lg bg-muted flex items-center justify-center">
                <span className="text-xs font-bold text-foreground">
                  {comparison.name[0]}
                </span>
              </div>
            </div>
            <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" />
          </div>

          {/* Content */}
          <div>
            <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors duration-300">
              Inloop vs {comparison.name}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {comparison.description}
            </p>
          </div>

          <div className="mt-4 pt-4 border-t border-border/50">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <div className="w-2 h-2 rounded-full bg-green-500" />
              <span>In-depth comparison</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
