import ComparisonCTA from "./ComparisonCTA";
import FAQSection from "../../components/FAQSection";

interface FAQWithCTAProps {
  competitorName: string;
  faqs: Array<{
    question: string;
    answer: string;
  }>;
  ctaContent: {
    title: string;
    description: string;
  };
}

export default function FAQWithCTA({
  competitorName,
  faqs,
  ctaContent,
}: FAQWithCTAProps) {
  return (
    <section className="pt-8 pb-16 px-6">
      <FAQSection
        heading="Frequently Asked Questions"
        subheading={`Compare ${competitorName} vs Inloop`}
        faqItems={faqs.map((faq) => ({
          question: faq.question,
          answer: faq.answer,
        }))}
      />
      <div className="max-w-2xl mx-auto mt-12">
        <ComparisonCTA
          title="Questions Answered. Time to Act."
          description="Thousands switched after seeing this comparison. Your turn."
          buttonText="Try Inloop for Free"
          buttonHref="https://william.tryinloop.com"
          variant="default"
          size="md"
          showTrustIndicators={true}
        />
      </div>
    </section>
  );
}
