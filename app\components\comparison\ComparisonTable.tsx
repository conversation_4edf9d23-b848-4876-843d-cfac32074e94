import { Button } from "@/components/ui/button";

interface ComparisonTableProps {
  headline: string;
  comparisonTable: Array<{
    feature: string;
    inloop: string;
    competitor: string;
  }>;
  conclusion?: string;
  competitorName: string;
}

export default function ComparisonTable({
  headline,
  comparisonTable,
  conclusion,
  competitorName,
}: ComparisonTableProps) {
  return (
    <section className="py-24 px-6">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 text-foreground">
          {headline}
        </h2>

        <div className="overflow-x-auto">
          <table className="w-full bg-card rounded-xl shadow-lg border border-border">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left p-3 sm:p-4 md:p-6 font-semibold text-foreground text-sm sm:text-base">
                  Feature
                </th>
                <th className="text-left p-3 sm:p-4 md:p-6 font-semibold text-primary text-sm sm:text-base">
                  Inloop
                </th>
                <th className="text-left p-3 sm:p-4 md:p-6 font-semibold text-muted-foreground text-sm sm:text-base">
                  {competitorName}
                </th>
              </tr>
            </thead>
            <tbody>
              {comparisonTable.map((row, index) => (
                <tr
                  key={index}
                  className="border-b border-border hover:bg-muted/50"
                >
                  <td className="p-3 sm:p-4 md:p-6 font-medium text-foreground text-sm sm:text-base">
                    {row.feature}
                  </td>
                  <td className="p-3 sm:p-4 md:p-6 text-foreground text-sm sm:text-base">
                    {row.inloop}
                  </td>
                  <td className="p-3 sm:p-4 md:p-6 text-muted-foreground text-sm sm:text-base">
                    {row.competitor}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="text-center mt-8 sm:mt-12">
          <p className="text-lg sm:text-xl md:text-2xl font-semibold mb-4 sm:mb-6 text-foreground">
            {conclusion}
          </p>
          <a
            href="https://william.tryinloop.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block"
          >
            <Button
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 px-8 py-3 cursor-pointer shadow-md hover:shadow-lg"
            >
              Try Inloop for Free
            </Button>
          </a>
        </div>
      </div>
    </section>
  );
}
