import { CheckCircle } from "lucide-react";
import TextGradient from "@/components/ui/text-gradient";
import AnimatedButton from "../common/AnimatedButton";

interface HeroSectionProps {
  headline: string;
  subheadline: string;
  description: string;
  featuresHighlight: string[];
}

export default function ComparisonHero({
  headline,
  subheadline,
  description,
  featuresHighlight,
}: HeroSectionProps) {
  return (
    <section className="min-h-screen flex items-center justify-center px-6 py-24 mt-16 sm:mt-20 md:mt-0">
      <div className="max-w-6xl mx-auto text-center">
        <h1 className="mb-6 text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
          <TextGradient txt={headline} />
        </h1>

        <p className="text-base sm:text-lg text-muted-foreground leading-relaxed sm:leading-relaxed md:text-xl md:leading-relaxed max-w-3xl sm:max-w-4xl mx-auto mb-6 font-semibold">
          {subheadline}
        </p>

        <p className="text-sm sm:text-base md:text-lg lg:text-xl text-muted-foreground leading-relaxed sm:leading-relaxed md:leading-relaxed lg:leading-relaxed max-w-2xl sm:max-w-3xl md:max-w-4xl mx-auto mb-8">
          {description}
        </p>

        <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 max-w-lg sm:max-w-none mx-auto">
          {featuresHighlight.map((feature, index) => (
            <span
              key={index}
              className="flex items-center gap-x-1 sm:gap-x-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-white/80 border border-gray-200 text-gray-700 rounded-full text-xs sm:text-sm font-medium shadow-sm w-[calc(50%-0.25rem)] sm:w-auto justify-center"
            >
              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-primary flex-shrink-0" />
              <span className="truncate text-center">{feature}</span>
            </span>
          ))}
        </div>

        <div className="flex flex-col items-center gap-6">
          <AnimatedButton
            size="lg"
            showArrow={true}
            href="https://william.tryinloop.com"
          >
            Try Inloop for Free
          </AnimatedButton>

          <div className="flex items-center justify-center gap-x-6 text-sm text-muted-foreground flex-wrap">
            <span className="flex items-center gap-x-1 whitespace-nowrap">
              <CheckCircle className="w-4 h-4 text-primary" />
              No Credit Card Required
            </span>
            <span className="flex items-center gap-x-1 whitespace-nowrap">
              <CheckCircle className="w-4 h-4 text-primary" />
              Free Forever Plan
            </span>
          </div>
        </div>
      </div>
    </section>
  );
}
